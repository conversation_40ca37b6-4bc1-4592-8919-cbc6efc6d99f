# %%导入库
import os
import sys
import numpy as np
from PIL import Image
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, 
                             QLabel, QVBoxLayout, QGridLayout, QSplitter, QSizePolicy,
                             QSpacerItem, QMessageBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.patches as mpatches
import matplotlib.pyplot as plt
import SAXS_CRC as sc
from tkinter import filedialog
from scipy.stats import binned_statistic
import shutil


# %% 主界面设计
class TwoDpattern(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('TwoDpattern')
        self.setGeometry(50, 200, 1500, 800)  # 初始窗口尺寸
        
        # 主窗口部件
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(10)
        
        # % 上方按钮网格
        button_upper_grid = QGridLayout()
        button_upper_grid.setHorizontalSpacing(30)
        button_upper_grid.setVerticalSpacing(10)
        
        self.btn_load = QPushButton("导入数据")        
        self.btn_find_center = QPushButton("确定圆心")
        self.btn_calibrate = QPushButton("坐标变换")
        self.btn_wait_2 = QPushButton("待开发2")
        self.btn_save = QPushButton("单次结果保存")
        self.btn_analysis_batch = QPushButton("批处理")        
        
        button_upper_grid.addWidget(self.btn_load, 0, 0)        
        button_upper_grid.addWidget(self.btn_find_center, 0, 1)
        button_upper_grid.addWidget(self.btn_calibrate, 0, 2)
        button_upper_grid.addWidget(self.btn_wait_2, 0, 3)
        button_upper_grid.addWidget(self.btn_save, 0, 4)
        button_upper_grid.addWidget(self.btn_analysis_batch, 0, 5)        
        
        # % 下方绘图和参数区
        bottom_splitter = QSplitter(Qt.Horizontal)
        
        # 绘图区
        self.fig1 = Figure(figsize=(5, 4)) 
        self.canvas1 = FigureCanvas(self.fig1)
        self.ax1 = self.fig1.add_subplot(111)
        # self.fig1, self.ax1 = plt.subplots(figsize=(10, 8))
        # self.fig1.canvas.manager.set_window_title('plot module')
        
        # 创建参数容器
        para_container = QWidget()
        para_layout = QVBoxLayout(para_container)
        para_layout.setContentsMargins(5, 5, 5, 5)  # 统一内边距
        
        # 注释标签-完全居中
        label_note = QLabel("规定X方向为X-ray,Y方向为探测器水平,Z方向为探测器竖直")
        label_note.setAlignment(Qt.AlignCenter)
        label_note.setStyleSheet("font-weight: bold; margin-bottom: 10px;")  # 添加视觉分隔
        para_layout.addWidget(label_note)
        
        # 使用QGridLayout实现3行2列布局
        grid_layout = QGridLayout()
        grid_layout.setVerticalSpacing(2)
        grid_layout.setHorizontalSpacing(5)
        
        # 创建参数控件
        self.edit_centery = QDoubleSpinBox(); self.edit_centerz = QDoubleSpinBox();
        self.edit_distance = QDoubleSpinBox(); self.edit_pixel = QDoubleSpinBox();
        self.edit_lambda_Xray = QDoubleSpinBox(); self.edit_incident_angle = QDoubleSpinBox();
        self.edit_I_min = QDoubleSpinBox(); self.edit_I_max = QDoubleSpinBox();
        self.edit_qxy1_min = QDoubleSpinBox(); self.edit_qxy1_max = QDoubleSpinBox();
        self.edit_qz1_min = QDoubleSpinBox(); self.edit_qz1_max = QDoubleSpinBox();
        self.edit_qy2_min = QDoubleSpinBox(); self.edit_qy2_max = QDoubleSpinBox();
        self.edit_qz2_min = QDoubleSpinBox(); self.edit_qz2_max = QDoubleSpinBox();
        self.edit_radi_integ_angle = QDoubleSpinBox(); self.edit_radi_integ_range = QDoubleSpinBox();
        self.edit_azuth_integ_q = QDoubleSpinBox(); self.edit_azuth_integ_q_range = QDoubleSpinBox();  
        
        for spin_box in [
            self.edit_centery, self.edit_centerz, self.edit_distance, self.edit_pixel,
            self.edit_lambda_Xray, self.edit_incident_angle, self.edit_I_min, self.edit_I_max,
            self.edit_qxy1_min, self.edit_qxy1_max, self.edit_qz1_min, self.edit_qz1_max,
            self.edit_qy2_min, self.edit_qy2_max, self.edit_qz2_min, self.edit_qz2_max,
            self.edit_radi_integ_angle, self.edit_radi_integ_range, self.edit_azuth_integ_q, 
            self.edit_azuth_integ_q_range]:
            spin_box.setRange(-1e10, 1e10); spin_box.setMaximumWidth(80); spin_box.setDecimals(3)
        
        # 1. 径向积分角度、径向积分范围 - 淡红色
        self.edit_radi_integ_angle.setStyleSheet("background-color: #ffcccc;")
        self.edit_radi_integ_range.setStyleSheet("background-color: #ffcccc;")
        
        # 2. 方位积分qc、方位角范围 - 淡蓝色
        self.edit_azuth_integ_q.setStyleSheet("background-color: #cce5ff;")
        self.edit_azuth_integ_q_range.setStyleSheet("background-color: #cce5ff;")
        
        # 3. distance、pixel - 淡黄色
        self.edit_distance.setStyleSheet("background-color: #ffffcc;")
        self.edit_pixel.setStyleSheet("background-color: #ffffcc;")
        
        # 4. 强度下限、强度上限 - 淡绿色
        self.edit_I_min.setStyleSheet("background-color: #ccffcc;")
        self.edit_I_max.setStyleSheet("background-color: #ccffcc;")
        
        # 5. X-ray波长、掠入射角 - 淡紫色
        self.edit_lambda_Xray.setStyleSheet("background-color: #e6ccff;")
        self.edit_incident_angle.setStyleSheet("background-color: #e6ccff;")
        
        # 6. 其他10个数值框 - 灰色
        gray_boxes = [
            self.edit_centery, self.edit_centerz,
            self.edit_qxy1_min, self.edit_qxy1_max,
            self.edit_qz1_min, self.edit_qz1_max,
            self.edit_qy2_min, self.edit_qy2_max,
            self.edit_qz2_min, self.edit_qz2_max]
        
        for box in gray_boxes:
            box.setStyleSheet("background-color: #e0e0e0;")
        
        grid_layout.addWidget(QLabel("Center_Y:"), 0, 0)
        grid_layout.addWidget(self.edit_centery, 0, 1)        
        grid_layout.addWidget(QLabel("Center_Z:"), 0, 2)
        grid_layout.addWidget(self.edit_centerz, 0, 3)        
        grid_layout.addWidget(QLabel("distance:"), 1, 0)
        grid_layout.addWidget(self.edit_distance, 1, 1)        
        grid_layout.addWidget(QLabel("pixel:"), 1, 2)
        grid_layout.addWidget(self.edit_pixel, 1, 3)        
        grid_layout.addWidget(QLabel("X-ray波长:"), 2, 0)
        grid_layout.addWidget(self.edit_lambda_Xray, 2, 1)        
        grid_layout.addWidget(QLabel("掠入射角:"), 2, 2)
        grid_layout.addWidget(self.edit_incident_angle, 2, 3)        
        grid_layout.addWidget(QLabel("强度下限:"), 3, 0)
        grid_layout.addWidget(self.edit_I_min, 3, 1)
        grid_layout.addWidget(QLabel("强度上限:"), 3, 2)
        grid_layout.addWidget(self.edit_I_max, 3, 3)        
        grid_layout.addWidget(QLabel("qxy1下限:"), 4, 0)
        grid_layout.addWidget(self.edit_qxy1_min, 4, 1)
        grid_layout.addWidget(QLabel("qxy1上限:"), 4, 2)
        grid_layout.addWidget(self.edit_qxy1_max, 4, 3)        
        grid_layout.addWidget(QLabel("qz1下限:"), 4, 4)
        grid_layout.addWidget(self.edit_qz1_min, 4, 5)
        grid_layout.addWidget(QLabel("qz1上限:"), 4, 6)
        grid_layout.addWidget(self.edit_qz1_max, 4, 7)        
        grid_layout.addWidget(QLabel("qy2下限:"), 5, 0)
        grid_layout.addWidget(self.edit_qy2_min, 5, 1)
        grid_layout.addWidget(QLabel("qy2上限:"), 5, 2)
        grid_layout.addWidget(self.edit_qy2_max, 5, 3)        
        grid_layout.addWidget(QLabel("qz2下限:"), 5, 4)
        grid_layout.addWidget(self.edit_qz2_min, 5, 5)
        grid_layout.addWidget(QLabel("qz2上限:"), 5, 6)
        grid_layout.addWidget(self.edit_qz2_max, 5, 7)
        
        grid_layout.addWidget(QLabel("径向积分角度:"), 0, 4)
        grid_layout.addWidget(self.edit_radi_integ_angle, 0, 5)
        grid_layout.addWidget(QLabel("径向积分范围:"), 0, 6)
        grid_layout.addWidget(self.edit_radi_integ_range, 0, 7)
        grid_layout.addWidget(QLabel("方位积分qc:"), 1, 4)
        grid_layout.addWidget(self.edit_azuth_integ_q, 1, 5)
        grid_layout.addWidget(QLabel("方位积分范围:"), 1, 6)
        grid_layout.addWidget(self.edit_azuth_integ_q_range, 1, 7)           
        
        for col in range(8):
            stretch = 1 if col % 2 == 0 else 2
            grid_layout.setColumnStretch(col, stretch)        
        
        para_layout.addLayout(grid_layout)
        # para_layout.addStretch(1)  # 底部弹性空间
        
        # % 右侧按钮网格
        button_grid_container = QWidget()
        button_right_layout = QGridLayout(button_grid_container)
        self.button_plot_trans = QPushButton("图像翻转")
        self.button_plot_pixel = QPushButton("pixel-pixel")
        self.button_plot_qxy1qz1 = QPushButton("qxy1-qz1")
        self.button_plot_qy2qz2 = QPushButton("qy2-qz2")        
        self.button_radi_see = QPushButton("径向预览")
        self.button_radi_integ = QPushButton("径向积分")
        self.button_azuth_see = QPushButton("方位角预览")
        self.button_azuth_integ = QPushButton("方位角积分")
        button_right_layout.addWidget(self.button_plot_trans,0,0)
        button_right_layout.addWidget(self.button_plot_pixel,0,1)
        button_right_layout.addWidget(self.button_plot_qxy1qz1,0,2)
        button_right_layout.addWidget(self.button_plot_qy2qz2,0,3)        
        button_right_layout.addWidget(self.button_radi_see,1,0)
        button_right_layout.addWidget(self.button_radi_integ,1,1)
        button_right_layout.addWidget(self.button_azuth_see,1,2)
        button_right_layout.addWidget(self.button_azuth_integ,1,3)
        para_layout.addWidget(button_grid_container)
        
        # 右侧容器布局
        right_container = QWidget()
        right_layout = QVBoxLayout(right_container)
        right_layout.addWidget(para_container)
        right_layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # 添加部件到splitter
        bottom_splitter.addWidget(self.canvas1)
        bottom_splitter.addWidget(right_container)
        bottom_splitter.setSizes([900, 100])  # 设置初始分割比例
        
        # 组合主布局
        main_layout.addLayout(button_upper_grid)
        main_layout.addWidget(bottom_splitter, 1)
        self.setCentralWidget(main_widget)
        
        # 数值初始化        
        self.cbar1 = None; self.fig2 = None; self.fig3 = None; self.last_wedge = None;        
        
        
        ####################################################################################
        # self.edit_centery.setValue(1092); self.edit_centerz.setValue(386);
        self.edit_distance.setValue(345); self.edit_pixel.setValue(0.139);
        self.edit_lambda_Xray.setValue(0.69); self.edit_incident_angle.setValue(0.22);
        self.edit_I_min.setValue(1); self.edit_I_max.setValue(2000);
        # self.edit_qxy1_min.setValue(-3.2); self.edit_qxy1_max.setValue(3.2);
        # self.edit_qz1_min.setValue(-1); self.edit_qz1_max.setValue(4.8);
        # self.edit_qy2_min.setValue(-3.2); self.edit_qy2_max.setValue(3.2);
        # self.edit_qz2_min.setValue(-1); self.edit_qz2_max.setValue(4.8);
        self.edit_radi_integ_angle.setValue(90); self.edit_radi_integ_range.setValue(5);
        self.edit_azuth_integ_q.setValue(2.25); self.edit_azuth_integ_q_range.setValue(0.2);
        ####################################################################################
        
        
        # 详细功能
        self.btn_load.clicked.connect(self.open_load) # 功能一：导入数据
        self.button_plot_trans.clicked.connect(self.pic_trans) # 功能二：图像翻转
        self.button_plot_pixel.clicked.connect(self.pixel_pixel_plot) # 功能三：绘制pixel图
        self.btn_calibrate.clicked.connect(self.para_calibrate) # 功能四：参数标定
        self.button_plot_qxy1qz1.clicked.connect(self.qxy1_qz1_plot) # 功能五：绘制GIXRS二维图
        self.button_plot_qy2qz2.clicked.connect(self.qy2_qz2_plot) # 功能六：绘制常规qyqz二维图
        self.button_radi_see.clicked.connect(self.radi_see_func) # 功能七：径向积分预览
        self.button_radi_integ.clicked.connect(self.radi_integ_func) # 功能八：径向积分
        self.button_azuth_see.clicked.connect(self.azuth_see_func) # 功能九：方位角积分预览
        self.button_azuth_integ.clicked.connect(self.azuth_integ_func) # 功能十：方位角积分
        self.btn_save.clicked.connect(self.save_pic_txt_func) # 功能十一：保存图像和文本
        self.btn_analysis_batch.clicked.connect(self.batch_func) # 功能十二：批处理
        self.btn_find_center.clicked.connect(self.find_center_func) # 功能十三：确定圆心        
        
        # 各个按钮状态管理
        self.btn_load.setEnabled(True); self.btn_analysis_batch.setEnabled(False);
        self.btn_calibrate.setEnabled(False); self.btn_save.setEnabled(False);
        self.btn_find_center.setEnabled(False); self.btn_wait_2.setEnabled(False); 
        
        self.button_plot_trans.setEnabled(False); self.button_plot_pixel.setEnabled(False); 
        self.button_plot_qxy1qz1.setEnabled(False); self.button_plot_qy2qz2.setEnabled(False); 
        self.button_radi_see.setEnabled(False); self.button_radi_integ.setEnabled(False); 
        self.button_azuth_see.setEnabled(False); self.button_azuth_integ.setEnabled(False); 
        
        # 添加按钮提示信息
        self.btn_calibrate.setToolTip("请确认参数区前三行的前两列数值已正确输入!")
        self.button_plot_qxy1qz1.setToolTip("请确认参数区第四和第五行已正确输入!")
        self.button_plot_qy2qz2.setToolTip("请确认参数区第四和第六行已正确输入!")
        self.btn_analysis_batch.setToolTip("使用前务必将右侧参数区填写完整且正确!");
        
        
# 主界面到此结束
############################################################################################################
        
        
    # 功能一：导入数据
    def open_load(self):
        # 弹出文件选择对话框
        self.file_path = filedialog.askopenfilename(
            title='Select a TIFF file', filetypes=[('TIFF files', '*.tif')])

        # 检查用户是否选择了文件
        if not self.file_path:
            return  # 用户取消了文件选择，直接返回

        self.pathName=os.path.dirname(self.file_path)
        self.fileName = os.path.basename(self.file_path)

        try:
            self.pattern = np.array(Image.open(self.file_path))
            self.Y, self.Z = np.meshgrid(
                sc.arange(0,1,self.pattern.shape[1]-1),
                sc.arange(self.pattern.shape[0]-1,-1,0))
            # 显示完成提示
            QMessageBox.information(self, "数据导入",
                                    "数据导入已完成", QMessageBox.Ok)
            # 激活pixel图绘制
            self.button_plot_pixel.setEnabled(True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件: {str(e)}", QMessageBox.Ok)
    
    
    # 功能二：图像翻转
    def pic_trans(self):
        self.pattern = np.flip(self.pattern, axis=0)
        self.panduan = 1
        # 显示完成提示
        QMessageBox.information(self, "图像翻转",
                                "图像翻转已完成", QMessageBox.Ok)
    
    
    # 功能三：绘制pixel图
    def pixel_pixel_plot(self): 
                
        # 清除现有colorbar
        if self.cbar1 is not None:
            self.cbar1.remove()
            self.cbar1 = None
        # 清除axes内容
        self.ax1.clear()                
        self.pic1 = self.ax1.pcolormesh(self.Y, self.Z, self.pattern, shading='auto', cmap='jet')
        self.cbar1 = self.fig1.colorbar(self.pic1, ax=self.ax1, pad=0.02)
        self.ax1.set_xlabel('Y(pixel)', fontsize=12)
        self.ax1.set_ylabel('Z(pixel)', fontsize=12)
        self.ax1.set_aspect('equal')
        self.ax1.set_xlim([np.min(self.Y[0,:]), np.max(self.Y[0,:])])
        self.ax1.set_ylim([np.min(self.Z[:,0]), np.max(self.Z[:,0])])
        self.ax1.set_title(self.fileName)        
        
        if (self.edit_I_min.value() < self.edit_I_max.value()):             
            self.pic1.set_clim(self.edit_I_min.value(), self.edit_I_max.value())
        
        self.canvas1.draw()
        # 显示完成提示
        QMessageBox.information(self, "图像绘制",
                                "pixel图像已绘制", QMessageBox.Ok)
        # 激活图像翻转和参数标定
        self.button_plot_trans.setEnabled(True)
        self.btn_calibrate.setEnabled(True)
        self.btn_find_center.setEnabled(True)
        
    
    # 功能四：参数标定
    def para_calibrate(self):
        Center_y = self.edit_centery.value(); Center_z = self.edit_centerz.value();
        distance = self.edit_distance.value(); pixel = self.edit_pixel.value();
        lambda_Xray = self.edit_lambda_Xray.value(); incident_angle = self.edit_incident_angle.value();
        Y = self.Y; Z = self.Z; pattern = self.pattern;

        v_I0 = [-1, 0, 0]; v_rotate_axis = [0, -1, 0];
        v_I_GIXRS = sc.rotateVector(v_I0, v_rotate_axis, incident_angle);
        ls_Y = (Y-Center_y)*pixel; ls_Z = (Z-Center_z)*pixel;
        ls_X=-distance*np.ones_like(ls_Y);
        norm_detector = np.sqrt(ls_X**2 + ls_Y**2 + ls_Z**2);
        vx = ls_X/norm_detector; vy = ls_Y/norm_detector; vz = ls_Z/norm_detector;
        sx = vx-v_I_GIXRS[0]; sy = vy-v_I_GIXRS[1]; sz = vz-v_I_GIXRS[2];
        
        # % qxy-qz模块
        qx=(2*np.pi/lambda_Xray)*sx; qy=(2*np.pi/lambda_Xray)*sy; qz=(2*np.pi/lambda_Xray)*sz; 
        q = np.sqrt(qx**2+qy**2+qz**2); qxy1 = np.sqrt(qx**2+qy**2); 
        qxy1[qy<0] = -qxy1[qy<0]; qz1 = qz;
        
        # % qy2-qz2模块
        self.norm_YZ = np.sqrt(ls_Y**2 + ls_Z**2); phi = np.acos(ls_Y/self.norm_YZ)*180/np.pi;
        phi[qz<0] = 360-phi[qz<0]; phi[np.isnan(phi)]=0;
        qy2 = q*np.cos(phi*np.pi/180); qz2 = q*np.sin(phi*np.pi/180);
        
        # % 构造GIXRS劈裂数据：qxy1-qz1
        p1=np.where(qxy1[0,:] < 0)[0][-1]; p2=np.where(qxy1[0,:] > 0)[0][0];
        add_qxy = np.column_stack([qxy1[:,p1]+1e-3, np.zeros_like(qxy1[:,p1]), qxy1[:,p2]-1e-3]);
        add_qz = np.column_stack([qz1[:,p1], 0.5*(qz1[:,p1]+qz1[:,p2]), qz1[:,p2]]);
        add_pattern = np.ones_like(add_qz)*np.min(pattern);
        qxy1 = np.column_stack([qxy1[:,0:p1+1],add_qxy,qxy1[:,p2:]]);
        qz1 = np.column_stack([qz1[:,0:p1+1],add_qz,qz1[:,p2:]]);
        pattern1 = np.column_stack([pattern[:,0:p1+1],add_pattern,pattern[:,p2:]]);
        
        self.pattern1 = pattern1; self.qxy1 = qxy1; self.qz1 = qz1;
        self.q = q; self.qy2 = qy2; self.qz2 = qz2; self.phi = phi;
        
        # 激活散射矢量图绘制
        self.button_plot_qxy1qz1.setEnabled(True)
        self.button_plot_qy2qz2.setEnabled(True)
        
        # 更改二维图的显示范围
        qxy1_min_cal = np.max(qxy1[:,0]); qxy1_max_cal = np.min(qxy1[:,-1]);
        qz1_min_cal = np.max(qz1[-1,:]); qz1_max_cal = np.min(qz1[0,:]);
        self.edit_qxy1_min.setValue(qxy1_min_cal); self.edit_qxy1_max.setValue(qxy1_max_cal);
        self.edit_qz1_min.setValue(qz1_min_cal); self.edit_qz1_max.setValue(qz1_max_cal);        
        qy2_min_cal = np.max(qy2[:,0]); qy2_max_cal = np.min(qy2[:,-1]);
        qz2_min_cal = np.max(qz2[-1,:]); qz2_max_cal = np.min(qz2[0,:]);
        self.edit_qy2_min.setValue(qy2_min_cal); self.edit_qy2_max.setValue(qy2_max_cal);
        self.edit_qz2_min.setValue(qz2_min_cal); self.edit_qz2_max.setValue(qz2_max_cal);
        # 显示完成提示
        QMessageBox.information(self, "参数标定",
                                "散射矢量标定已完成", QMessageBox.Ok)
    
    
    # 功能五：绘制GIXRS二维图
    def qxy1_qz1_plot(self):        
        # 检查并关闭已存在的图形窗口
        if hasattr(self, 'fig2'):
            plt.close(self.fig2)
            del self.fig2
            
        self.fig2, self.ax2 = plt.subplots(figsize=(10, 8));
        self.fig2.canvas.manager.set_window_title('qxy-qz Color Map with missing wedge');
        self.pic2 = self.ax2.pcolormesh(self.qxy1, self.qz1, self.pattern1, shading='auto', cmap='jet');
        self.cbar2 = self.fig2.colorbar(self.pic2, ax=self.ax2, pad=0.02);
        self.ax2.set_xlabel(r'$q\mathregular{_{xy}\/(\AA^{-1}})$', family="Arial", fontsize=12); 
        self.ax2.set_ylabel(r'$q\mathregular{_{z}\/(\AA^{-1}})$', family="Arial", fontsize=12);
        self.ax2.set_aspect('equal');        
        self.ax2.set_title(self.fileName);        
        self.pic2.set_cmap("jet");
        
        if (self.edit_qxy1_min.value() < self.edit_qxy1_max.value()):             
            self.ax2.set_xlim([self.edit_qxy1_min.value(), self.edit_qxy1_max.value()]);
        if (self.edit_qz1_min.value() < self.edit_qz1_max.value()):             
            self.ax2.set_ylim([self.edit_qz1_min.value(), self.edit_qz1_max.value()]);
        if (self.edit_I_min.value() < self.edit_I_max.value()):             
            self.pic2.set_clim(self.edit_I_min.value(), self.edit_I_max.value());     
    
    
    # 功能六：绘制常规qyqz二维图
    def qy2_qz2_plot(self):        
        # 检查并关闭已存在的图形窗口
        if hasattr(self, 'fig3'):
            plt.close(self.fig3)
            del self.fig3
            
        self.fig3, self.ax3 = plt.subplots(figsize=(10, 8));
        self.fig3.canvas.manager.set_window_title('qy-qz Color Map');
        self.pic3 = self.ax3.pcolormesh(self.qy2, self.qz2, self.pattern, shading='auto', cmap='jet');
        self.cbar3 = self.fig3.colorbar(self.pic3, ax=self.ax3, pad=0.02);
        self.ax3.set_xlabel(r'$q\mathregular{_{y}\/(\AA^{-1}})$', family="Arial", fontsize=12); 
        self.ax3.set_ylabel(r'$q\mathregular{_{z}\/(\AA^{-1}})$', family="Arial", fontsize=12);
        self.ax3.set_aspect('equal');        
        self.ax3.set_title(self.fileName);        
        self.pic3.set_cmap("jet");
        
        if (self.edit_qy2_min.value() < self.edit_qy2_max.value()):             
            self.ax3.set_xlim([self.edit_qy2_min.value(), self.edit_qy2_max.value()]);
        if (self.edit_qz2_min.value() < self.edit_qz2_max.value()):             
            self.ax3.set_ylim([self.edit_qz2_min.value(), self.edit_qz2_max.value()]);
        if (self.edit_I_min.value() < self.edit_I_max.value()):             
            self.pic3.set_clim(self.edit_I_min.value(), self.edit_I_max.value());  
        
        # 激活径向积分和单次结果保存
        self.button_radi_see.setEnabled(True)
        self.btn_save.setEnabled(True)
       
    # 功能七：径向积分预览
    def radi_see_func(self):
        # 定义阴影区域参数
        center_angle = self.edit_radi_integ_angle.value()   # 中心角度（竖直向上）
        angle_width = self.edit_radi_integ_range.value()    # 总角度宽度（中心左右各10度）
        radius = np.max(self.q)          # 阴影区半径
        
        # 计算扇形边界
        theta1 = center_angle - angle_width/2  # 起始角度 80度
        theta2 = center_angle + angle_width/2  # 结束角度 100度        
        
        # 创建扇形补丁
        wedge = mpatches.Wedge(
            center=(0, 0),   # 原点位置
            r=radius,        # 半径
            theta1=theta1,   # 起始角度
            theta2=theta2,    # 结束角度
            alpha=0.2,       # 透明度
            color='red',    # 填充颜色
            edgecolor='black',# 边界颜色
            linewidth=1.5)    # 边界线宽
        
        # 移除上次添加的扇形（如果存在）
        if self.last_wedge is not None and self.last_wedge in self.ax3.patches:
            self.last_wedge.remove()
        
        # 添加扇形到坐标轴
        self.ax3.add_patch(wedge)
        self.fig3.canvas.draw_idle() 
        self.last_wedge = wedge
        # 显示完成提示
        # QMessageBox.information(self, "图像预览",
        #                         "径向积分区域已设定", QMessageBox.Ok)
        # 激活径向积分
        self.button_radi_integ.setEnabled(True) 
        
        
    # 功能八：径向积分
    def radi_integ_func(self):
        alpha=self.edit_radi_integ_angle.value(); pixel=self.edit_pixel.value();
        alpha_range=self.edit_radi_integ_range.value();
        t1=np.abs(self.phi-alpha); t1[t1>alpha_range]=100;
        I1=np.column_stack([self.q[t1<alpha_range], self.pattern[t1<alpha_range]]);
        I1 = I1[np.argsort(I1[:,0])]
        L1 = self.norm_YZ[t1<alpha_range]; length_L = int(np.floor(np.max(L1)/pixel));
        q0 = np.linspace(np.min(I1[:,0]),np.max(I1[:,0]),length_L)
        stat, bin_edges, _ = binned_statistic(I1[:, 0], I1[:, 1], statistic='mean', bins=q0)
        Im = np.column_stack([bin_edges[:-1],stat])
        Im = Im[~np.isnan(Im[:,1])];
        
        self.fig4, self.ax4 = plt.subplots(figsize=(10, 8));
        self.fig4.canvas.manager.set_window_title('1D I-q integration');
        self.pic4 = self.ax4.plot(Im[:,0],Im[:,1]); 
        self.pic4[0].set_color('red'); self.pic4[0].set_linewidth(3);
        self.ax4.set_xlabel(r'$q\mathregular{\/(\AA^{-1}})$', family="Arial", fontsize=12, weight='bold');
        self.ax4.set_ylabel('I (a.u.)', family="Arial", fontsize=12, weight='bold');
        self.ax4.set_title(self.fileName, weight='bold');
        self.Im = Im;
        # 激活方位角预览
        self.button_azuth_see.setEnabled(True) 

        
    
    # 功能九：方位角预览
    def azuth_see_func(self):
        # 环形积分
        center = (0, 0)      # 圆心坐标
        radius = self.edit_azuth_integ_q.value()         # 平均半径
        width = self.edit_azuth_integ_q_range.value()          # 环宽
        start_angle = 0    # 起始角度（度）
        end_angle = 360      # 终止角度（度）
        wedge = mpatches.Wedge(
        center, 
        radius + width/2,   # 外半径 = 平均半径 + 半宽
        start_angle, 
        end_angle,
        width=width,        # 环宽
        alpha=0.2,          # 透明度设置为30%
        color='blue',       # 蓝色填充
        edgecolor='none'    # 无边框线
        )
        
        # 移除上次添加的扇形（如果存在）
        if self.last_wedge is not None and self.last_wedge in self.ax3.patches:
            self.last_wedge.remove()
        
        self.ax3.add_patch(wedge)
        self.fig3.canvas.draw_idle()
        self.last_wedge = wedge
        # 显示完成提示
        # QMessageBox.information(self, "图像预览",
        #                         "径向积分区域已设定", QMessageBox.Ok)
        # 激活方位角积分
        self.button_azuth_integ.setEnabled(True) 
        
        
    # 功能十：方位角积分
    def azuth_integ_func(self):
        q_a=self.edit_azuth_integ_q.value() 
        range_q=self.edit_azuth_integ_q_range.value()
        azimuthal_angle=np.linspace(0,360,361);
        q = self.q; pattern = self.pattern; phi = self.phi;
        
        t1=np.abs(q-q_a); t1[t1>range_q]=10;
        I1=np.column_stack([phi[t1<1],pattern[t1<1]]);
        I1 = I1[np.argsort(I1[:,0])] # 按照第一列排序
        stat, bin_edges, _ = binned_statistic(I1[:, 0], I1[:, 1], statistic='mean', bins=azimuthal_angle);
        In = np.column_stack([bin_edges[:-1],stat]);
        In = In[~np.isnan(In[:,1])];

        self.fig5, self.ax5 = plt.subplots(figsize=(10, 8));
        self.fig5.canvas.manager.set_window_title('1D I-phi azimuthal scan');
        self.pic5 = self.ax5.plot(In[:,0],In[:,1]); 
        self.pic5[0].set_color('blue'); self.pic5[0].set_linewidth(3);
        self.ax5.set_xlabel('phi (deg)', family="Arial", fontsize=12, weight='bold');
        self.ax5.set_ylabel('I (a.u.)', family="Arial", fontsize=12, weight='bold');
        self.ax5.set_title(self.fileName, weight='bold');
        self.In = In;
        # 激活批处理
        self.btn_analysis_batch.setEnabled(True) 
        
    # 功能十一：保存图像和文本
    def save_pic_txt_func(self):
        baseName = os.path.splitext(self.fileName)[0]
        analysis_folder = os.path.join(self.pathName,f"{baseName}_analysis")
        os.makedirs(analysis_folder, exist_ok=True)
        shutil.copy2(self.file_path, analysis_folder)
        
        if hasattr(self, 'fig1') and self.fig1 is not None:
            self.fig1.savefig(os.path.join(analysis_folder,f"{baseName}_pixel_2D_pattern.png"), 
                              dpi=200, bbox_inches='tight')

        if hasattr(self, 'fig2') and self.fig2 is not None:
            self.fig2.savefig(os.path.join(analysis_folder,f"{baseName}_qz-qxy_2D_pattern.png"), 
                              dpi=200, bbox_inches='tight')
        
        if hasattr(self, 'fig3') and self.fig3 is not None:
            self.fig3.savefig(os.path.join(analysis_folder,f"{baseName}_qz-qy_2D_pattern.png"), 
                              dpi=200, bbox_inches='tight')
        
        if hasattr(self, 'fig4') and self.fig4 is not None:
            self.fig4.savefig(os.path.join(analysis_folder,f"{baseName}_I-q_1D_integration.png"), 
                              dpi=200, bbox_inches='tight')
        
        if hasattr(self, 'fig5') and self.fig5 is not None:
            self.fig5.savefig(os.path.join(analysis_folder,f"{baseName}_I-phi_1D_integration.png"), 
                              dpi=200, bbox_inches='tight')
        
        if hasattr(self, 'Im') and self.Im is not None:
            np.savetxt(os.path.join(analysis_folder,f"{baseName}_I-q_data.txt"), 
                       self.Im, fmt='%.6f', delimiter='\t')
        
        if hasattr(self, 'In') and self.In is not None:
            np.savetxt(os.path.join(analysis_folder,f"{baseName}_I-phi_data.txt"), 
                       self.In, fmt='%.6f', delimiter='\t')    
        # 显示完成提示
        QMessageBox.information(self, "数据保存",
                                "数据已保存，文件夹已生成", QMessageBox.Ok)
    
    
    # 功能十二：批处理    
    def batch_func(self):        
        default_parameter = np.zeros([6,4])
        default_parameter[0, 0] = self.edit_centery.value(); 
        default_parameter[0, 1] = self.edit_centerz.value();
        default_parameter[0, 2] = self.edit_radi_integ_angle.value();
        default_parameter[0, 3] = self.edit_radi_integ_range.value();
        default_parameter[1, 0] = self.edit_distance.value(); 
        default_parameter[1, 1] = self.edit_pixel.value();
        default_parameter[1, 2] = self.edit_azuth_integ_q.value(); 
        default_parameter[1, 3] = self.edit_azuth_integ_q_range.value();
        default_parameter[2, 0] = self.edit_lambda_Xray.value(); 
        default_parameter[2, 1] = self.edit_incident_angle.value();
        default_parameter[3, 0] = self.edit_I_min.value(); 
        default_parameter[3, 1] = self.edit_I_max.value();
        default_parameter[4, 0] = self.edit_qxy1_min.value();
        default_parameter[4, 1] = self.edit_qxy1_max.value();
        default_parameter[4, 2] = self.edit_qz1_min.value(); 
        default_parameter[4, 3] = self.edit_qz1_max.value();
        default_parameter[5, 0] = self.edit_qy2_min.value(); 
        default_parameter[5, 1] = self.edit_qy2_max.value();
        default_parameter[5, 2] = self.edit_qz2_min.value();
        default_parameter[5, 3] = self.edit_qz2_max.value();
        
        all_files = os.listdir(self.pathName)
        filename0 = [
            f for f in all_files
            if f.lower().endswith(('.tif', '.tiff'))  # 匹配后缀（不区分大小写）
            and os.path.isfile(os.path.join(self.pathName, f))  # 确保是文件而非文件夹
        ]
        
        number_i = 0;
        for filename_i in filename0:            
            current_filename = filename_i; number_i = number_i + 1;
            sc.TwoDpattern_batch_son(self.pathName,current_filename,default_parameter,self.panduan)   
        # 显示完成提示
        QMessageBox.information(self, "数据保存",
                                "计算已完成", QMessageBox.Ok)
    
    
    # 功能十三：确定圆心
    def find_center_func(self):
        points = list()
        self.fig6, self.ax6 = plt.subplots(figsize=(10, 8));
        self.fig6.canvas.manager.set_window_title('Find center from pixel-pixel pattern');
        self.pic6 = self.ax6.pcolormesh(self.Y, self.Z, self.pattern, shading='auto', cmap='jet');
        self.cbar6 = self.fig6.colorbar(self.pic6, ax=self.ax6, pad=0.02);
        self.ax6.set_xlabel('Y(pixel)', family="Arial", fontsize=12); 
        self.ax6.set_ylabel('Z(pixel)', family="Arial", fontsize=12);
        self.ax6.set_aspect('equal'); self.ax6.set_title(self.fileName);   
        self.pic6.set_clim(self.edit_I_min.value(), self.edit_I_max.value())
        
        scatter = self.ax6.scatter([], [], c='b', s=50, marker='o')  # 初始空散点图
        while True:
            pts = plt.ginput(n=1, timeout=0)  # 等待点击
            if not pts:  # 回车退出
                break
            y_click, z_click = pts[0]
            points.append([y_click, z_click])
            # 标记蓝色圆点
            scatter.set_offsets(points)  # 关键：只更新散点数据
            plt.draw();  # self.canvas1.draw() # plt.draw(self.ax6)

        # 最小二乘法拟合
        points = np.array(points);
        x, y = points[:, 0], points[:, 1];
        A = np.column_stack([x, y, np.ones(len(x))]); B = -(x**2 + y**2);
        solution = np.linalg.lstsq(A, B, rcond=None)[0];
        # 计算圆心和半径
        xa = -solution[0] / 2; yb = -solution[1] / 2; # R = np.sqrt(xa**2 + yb**2 - solution[2]);
        # R_cal = np.sqrt((x - xa)**2 + (y - yb)**2); rms_error = np.sqrt(np.mean((R_cal - R)**2));
        center_y, center_z = round(xa), round(yb);
        self.ax6.scatter(center_y, center_z, c='r', s=50, marker='o');
        plt.draw();  # self.canvas1.draw() # plt.draw(self.ax6); 
        self.edit_centery.setValue(center_y); self.edit_centerz.setValue(center_z);

 

# %% 显示窗口
if __name__ == "__main__":
    app = QApplication(sys.argv) 
    window = TwoDpattern()
    window.show()
    sys.exit(app.exec_())        
        



