# PyInstaller 打包指令

## 完整打包流程

### 1. 更新 requirements.txt
```bash
pip freeze > requirements.txt
```

### 2. 创建虚拟环境并安装依赖
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source "venv/Scripts/activate"

# 安装依赖
pip install -r requirements.txt

# 安装 PyInstaller
pip install pyinstaller

# 运行此项目测试
 cd 打包教学
python LJ_Zhang_2D_pattern_V2.py
```


### 3. 打包命令

#### 基本打包（生成文件夹）
```bash
pyinstaller main.py
```

#### 打包成单个exe文件
```bash
pyinstaller --onefile main.py
```

#### GUI程序（无控制台窗口）
```bash
pyinstaller --onefile --windowed main.py
```

#### 带图标的完整打包
```bash
pyinstaller --onefile --windowed --icon=app.ico --name=MyApp main.py
```

### 4. 常用参数组合

| 命令 | 说明 |
|------|------|
| `--onefile` | 打包成单个exe |
| `--windowed` | 无控制台窗口 |
| `--icon=app.ico` | 指定图标 |
| `--name=AppName` | 指定exe名称 |
| `--clean` | 清理临时文件 |

### 5. 完成后清理
```bash
# 退出虚拟环境
deactivate

# 删除虚拟环境（可选）
rmdir /s venv
```

## 一键打包脚本

创建 `build.bat` 文件：
```batch
@echo off
echo 更新依赖列表...
pip freeze > requirements.txt

echo 创建虚拟环境...
python -m venv build_env

echo 激活虚拟环境...
call build_env\Scripts\activate

echo 安装依赖...
pip install -r requirements.txt
pip install pyinstaller

echo 开始打包...
pyinstaller --onefile --windowed --name=MyApp main.py

echo 清理环境...
deactivate
rmdir /s /q build_env

echo 打包完成！exe文件在 dist 文件夹中
pause
```
